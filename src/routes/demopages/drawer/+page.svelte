<script lang="ts">
  import { But<PERSON> } from '$lib/components/ui/button';
  import { Card, CardHeader, CardTitle, CardContent } from '$lib/components/ui/card';
  import { Input } from '$lib/components/ui/input';

  interface Job {
    id: string;
    title: string;
    company: string;
    location: string;
    salary: string;
    type: string;
    experience: string;
    status: string;
    priority: string;
    description: string;
    requirements: string[];
    benefits: string[];
  }

  let selectedJob = $state<Job | null>(null);
  let drawerOpen = $state(false);

  // Same mock data with more detailed info for drawer
  const mockJobs = [
    {
      id: '1',
      title: 'Senior Frontend Developer',
      company: 'TechCorp Inc',
      location: 'San Francisco, CA',
      salary: '$120k - $160k',
      status: 'Open',
      posted: '2 days ago',
      priority: 'A',
      type: 'Full-time',
      experience: 'Senior (5+ years)',
      description: 'We are looking for a Senior Frontend Developer to join our growing team. You will be responsible for building user-facing features using React, TypeScript, and modern frontend technologies.',
      requirements: [
        '5+ years of frontend development experience',
        'Expert knowledge of React and TypeScript',
        'Experience with modern build tools (Vite, Webpack)',
        'Strong CSS skills and responsive design',
        'Experience with testing frameworks (Jest, Cypress)'
      ],
      benefits: [
        'Competitive salary and equity',
        'Health, dental, and vision insurance',
        'Remote work flexibility',
        'Professional development budget'
      ]
    },
    {
      id: '2', 
      title: 'Product Manager',
      company: 'StartupXYZ',
      location: 'Remote',
      salary: '$90k - $130k',
      status: 'Open',
      posted: '1 week ago',
      priority: 'B',
      type: 'Full-time',
      experience: 'Mid-level (3-5 years)',
      description: 'Lead product strategy and roadmap development for our fast-growing SaaS platform.',
      requirements: [
        '3+ years of product management experience',
        'Technical background preferred',
        'Strong analytical and communication skills',
        'Experience with agile methodologies'
      ],
      benefits: [
        'Equity package',
        'Flexible PTO',
        'Remote-first culture',
        'Learning stipend'
      ]
    },
    {
      id: '3',
      title: 'UX Designer',
      company: 'DesignStudio',
      location: 'New York, NY',
      salary: '$80k - $110k',
      status: 'Draft',
      posted: '3 days ago',
      priority: 'C',
      type: 'Full-time',
      experience: 'Mid-level (3-5 years)',
      description: 'Create beautiful and intuitive user experiences for our design platform.',
      requirements: [
        'Portfolio showcasing UX/UI design work',
        'Proficiency in Figma and design systems',
        'User research and testing experience',
        'Collaborative mindset'
      ],
      benefits: [
        'Creative environment',
        'Design tool stipends',
        'Conference attendance',
        'Mentorship programs'
      ]
    },
    {
      id: '4',
      title: 'Backend Engineer',
      company: 'DataFlow Systems',
      location: 'Austin, TX',
      salary: '$100k - $140k',
      status: 'Filled',
      posted: '2 weeks ago',
      priority: 'A',
      type: 'Full-time',
      experience: 'Senior (5+ years)',
      description: 'Build scalable APIs and data processing systems for our analytics platform.',
      requirements: [
        'Experience with Node.js or Python',
        'Database design and optimization',
        'Cloud platforms (AWS, GCP)',
        'Microservices architecture'
      ],
      benefits: [
        'Stock options',
        'Health benefits',
        'Work-life balance',
        'Tech conferences'
      ]
    }
  ];

  // Open drawer with selected job
  function openJobDrawer(job: Job) {
    console.log('Opening drawer for job:', job.title); // Debug log
    selectedJob = job;
    drawerOpen = true;
    console.log('Drawer state:', drawerOpen); // Debug log
  }

  function closeDrawer() {
    drawerOpen = false;
    selectedJob = null;
  }

  function handleQuickAction(action: string) {
    alert(`${action} action would be implemented here for job: ${selectedJob?.title}`);
  }

  // Get status styling
  function getStatusClass(status: string) {
    switch (status) {
      case 'Open': return 'bg-green-100 text-green-800';
      case 'Draft': return 'bg-gray-100 text-gray-800';
      case 'Filled': return 'bg-blue-100 text-blue-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  }

  // Get priority styling
  function getPriorityClass(priority: string) {
    switch (priority) {
      case 'A': return 'bg-red-100 text-red-800';
      case 'B': return 'bg-orange-100 text-orange-800';
      case 'C': return 'bg-yellow-100 text-yellow-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  }
</script>

<svelte:head>
  <title>Drawer Pattern - Demo</title>
</svelte:head>

<div class="space-y-6">
  <!-- Demo Description -->
  <Card>
    <CardHeader>
      <CardTitle class="text-lg">Drawer/Slide-out Pattern</CardTitle>
    </CardHeader>
    <CardContent>
      <p class="text-muted-foreground text-sm">
        Click any job row to open a sliding drawer from the right. This keeps the list visible while 
        showing detailed information and actions in a side panel.
      </p>
    </CardContent>
  </Card>

  <!-- Jobs List -->
  <Card>
    <CardHeader>
      <CardTitle>Jobs List</CardTitle>
    </CardHeader>
    <CardContent>
      <div class="overflow-x-auto">
        <table class="w-full border-collapse">
          <thead>
            <tr class="border-b">
              <th class="text-left p-3 font-medium text-muted-foreground">Job Title</th>
              <th class="text-left p-3 font-medium text-muted-foreground">Company</th>
              <th class="text-left p-3 font-medium text-muted-foreground">Location</th>
              <th class="text-left p-3 font-medium text-muted-foreground">Salary</th>
              <th class="text-left p-3 font-medium text-muted-foreground">Status</th>
              <th class="text-left p-3 font-medium text-muted-foreground">Priority</th>
              <th class="text-left p-3 font-medium text-muted-foreground">Posted</th>
              <th class="text-left p-3 font-medium text-muted-foreground">Actions</th>
            </tr>
          </thead>
          <tbody>
            {#each mockJobs as job}
              <tr
                class="border-b hover:bg-muted/50 cursor-pointer transition-colors"
                onclick={() => openJobDrawer(job)}
                onkeydown={(e) => e.key === 'Enter' && openJobDrawer(job)}
                tabindex="0"
                role="button"
              >
                <td class="p-3">
                  <div class="font-medium">{job.title}</div>
                </td>
                <td class="p-3 text-muted-foreground">{job.company}</td>
                <td class="p-3 text-muted-foreground">{job.location}</td>
                <td class="p-3 text-muted-foreground">{job.salary}</td>
                <td class="p-3">
                  <span class="px-2 py-1 text-xs font-medium rounded-full {getStatusClass(job.status)}">
                    {job.status}
                  </span>
                </td>
                <td class="p-3">
                  <span class="px-2 py-1 text-xs font-medium rounded-full {getPriorityClass(job.priority)}">
                    {job.priority}
                  </span>
                </td>
                <td class="p-3 text-muted-foreground text-sm">{job.posted}</td>
                <td class="p-3">
                  <Button 
                    variant="ghost" 
                    size="sm" 
                    on:click={(e) => {
                      e.stopPropagation();
                      openJobDrawer(job);
                    }}
                  >
                    Open
                  </Button>
                </td>
              </tr>
            {/each}
          </tbody>
        </table>
      </div>
    </CardContent>
  </Card>

  <!-- Pattern Notes -->
  <Card>
    <CardHeader>
      <CardTitle class="text-sm">Pattern Notes</CardTitle>
    </CardHeader>
    <CardContent class="text-xs space-y-2">
      <div><strong>Pros:</strong> Keeps list context, rich detail space, modern UX, quick switching</div>
      <div><strong>Cons:</strong> Screen space limits, mobile complexity, state management overhead</div>
      <div><strong>Best for:</strong> Quick comparisons, repetitive tasks, rich content with actions</div>
    </CardContent>
  </Card>
</div>

<!-- Job Details Drawer - Simple Implementation -->
{#if drawerOpen}
  <!-- Overlay -->
  <div
    class="fixed inset-0 bg-black/50 z-40"
    onclick={closeDrawer}
    role="button"
    tabindex="0"
    onkeydown={(e) => e.key === 'Escape' && closeDrawer()}
  ></div>
  
  <!-- Drawer -->
  <div class="fixed right-0 top-0 h-full w-[600px] bg-background border-l shadow-lg z-50 overflow-y-auto">
    {#if selectedJob}
      <!-- Header -->
      <div class="border-b p-6">
        <div class="flex justify-between items-start">
          <div>
            <h2 class="text-xl font-semibold">{selectedJob.title}</h2>
            <p class="text-muted-foreground">{selectedJob.company}</p>
          </div>
          <Button variant="ghost" size="sm" on:click={closeDrawer}>
            ✕
          </Button>
        </div>
      </div>

      <!-- Content -->
      <div class="p-6 space-y-6">
        <!-- Quick Info Grid -->
        <div class="grid grid-cols-2 gap-4 text-sm">
          <div>
            <span class="font-medium text-muted-foreground">Location:</span>
            <p>{selectedJob.location}</p>
          </div>
          <div>
            <span class="font-medium text-muted-foreground">Salary:</span>
            <p>{selectedJob.salary}</p>
          </div>
          <div>
            <span class="font-medium text-muted-foreground">Type:</span>
            <p>{selectedJob.type}</p>
          </div>
          <div>
            <span class="font-medium text-muted-foreground">Experience:</span>
            <p>{selectedJob.experience}</p>
          </div>
        </div>

        <!-- Status Badges -->
        <div class="flex gap-2">
          <span class="px-2 py-1 text-xs font-medium rounded-full {getStatusClass(selectedJob.status)}">
            {selectedJob.status}
          </span>
          <span class="px-2 py-1 text-xs font-medium rounded-full {getPriorityClass(selectedJob.priority)}">
            Priority {selectedJob.priority}
          </span>
        </div>

        <!-- Description -->
        <div>
          <h3 class="font-medium mb-2">Description</h3>
          <p class="text-sm text-muted-foreground leading-relaxed">
            {selectedJob.description}
          </p>
        </div>

        <!-- Requirements -->
        <div>
          <h3 class="font-medium mb-2">Requirements</h3>
          <ul class="space-y-1">
            {#each selectedJob.requirements as requirement}
              <li class="flex items-start gap-2 text-sm">
                <span class="text-green-600 mt-1">✓</span>
                <span>{requirement}</span>
              </li>
            {/each}
          </ul>
        </div>

        <!-- Benefits -->
        <div>
          <h3 class="font-medium mb-2">Benefits</h3>
          <ul class="space-y-1">
            {#each selectedJob.benefits as benefit}
              <li class="flex items-start gap-2 text-sm">
                <span class="text-blue-600 mt-1">★</span>
                <span>{benefit}</span>
              </li>
            {/each}
          </ul>
        </div>

        <!-- Quick Actions Form -->
        <div class="border-t pt-4">
          <h3 class="font-medium mb-3">Quick Actions</h3>
          <div class="space-y-3">
            <div>
              <label for="job-status" class="text-sm font-medium">Status</label>
              <select id="job-status" class="w-full mt-1 px-3 py-2 border border-input rounded-md text-sm">
                <option value="open" selected={selectedJob.status === 'Open'}>Open</option>
                <option value="draft">Draft</option>
                <option value="filled">Filled</option>
                <option value="cancelled">Cancelled</option>
              </select>
            </div>
            <div>
              <label for="job-note" class="text-sm font-medium">Add Note</label>
              <Input id="job-note" placeholder="Internal note..." class="mt-1" />
            </div>
          </div>
        </div>
      </div>

      <!-- Footer -->
      <div class="border-t p-6 flex gap-2">
        <Button variant="outline" on:click={() => handleQuickAction('Edit')}>
          Edit Job
        </Button>
        <Button on:click={() => handleQuickAction('Save')}>
          Save Changes
        </Button>
      </div>
    {/if}
  </div>
{/if}
