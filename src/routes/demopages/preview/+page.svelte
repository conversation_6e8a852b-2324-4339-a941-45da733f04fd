<script lang="ts">
  import { But<PERSON> } from '$lib/components/ui/button';
  import { Card, CardHeader, CardTitle, CardContent } from '$lib/components/ui/card';

  interface Job {
    id: string;
    title: string;
    company: string;
    location: string;
    salary: string;
    type: string;
    experience: string;
    status: string;
    priority: string;
    description: string;
    requirements: string[];
    benefits: string[];
  }

  let selectedJob = $state<Job | null>(null);
  let hoveredJobId = $state<string | null>(null);
  let popoverPosition = $state({ x: 0, y: 0 });

  // Same mock data as traditional demo
  const mockJobs = [
    {
      id: '1',
      title: 'Senior Frontend Developer',
      company: 'TechCorp Inc',
      location: 'San Francisco, CA',
      salary: '$120k - $160k',
      status: 'Open',
      posted: '2 days ago',
      priority: 'A',
      description: 'Build user-facing features using React and TypeScript...',
      requirements: ['5+ years React', 'TypeScript expert', 'Modern tooling'],
      type: 'Full-time'
    },
    {
      id: '2', 
      title: 'Product Manager',
      company: 'StartupXYZ',
      location: 'Remote',
      salary: '$90k - $130k',
      status: 'Open',
      posted: '1 week ago',
      priority: 'B',
      description: 'Lead product strategy and roadmap development...',
      requirements: ['3+ years PM experience', 'Technical background', 'User-focused'],
      type: 'Full-time'
    },
    {
      id: '3',
      title: 'UX Designer',
      company: 'DesignStudio',
      location: 'New York, NY',
      salary: '$80k - $110k',
      status: 'Draft',
      posted: '3 days ago',
      priority: 'C',
      description: 'Create beautiful and intuitive user experiences...',
      requirements: ['Portfolio required', 'Figma expertise', 'User research'],
      type: 'Full-time'
    },
    {
      id: '4',
      title: 'Backend Engineer',
      company: 'DataFlow Systems',
      location: 'Austin, TX',
      salary: '$100k - $140k',
      status: 'Filled',
      posted: '2 weeks ago',
      priority: 'A',
      description: 'Build scalable APIs and data processing systems...',
      requirements: ['Node.js/Python', 'Database design', 'Cloud platforms'],
      type: 'Full-time'
    }
  ];

  // Show quick preview
  function showQuickPreview(job: Job, event: MouseEvent) {
    selectedJob = job;
    hoveredJobId = job.id;

    // Position popover to the right of the row, but keep it in viewport
    const rect = (event.currentTarget as HTMLElement).getBoundingClientRect();
    const viewportWidth = window.innerWidth;
    const popoverWidth = 320; // Estimated popover width

    let x = rect.right + 10;
    let y = rect.top;

    // If popover would go off-screen, position it to the left
    if (x + popoverWidth > viewportWidth) {
      x = rect.left - popoverWidth - 10;
    }

    // Keep within top/bottom bounds
    if (y < 10) y = 10;
    if (y > window.innerHeight - 200) y = window.innerHeight - 200;

    popoverPosition = { x, y };
  }

  function hidePreview() {
    hoveredJobId = null;
    selectedJob = null;
  }

  function viewFullDetails(jobId: string) {
    // Would navigate to full details in real implementation
    alert(`Navigate to full details for job ${jobId}`);
  }

  // Get status styling
  function getStatusClass(status: string) {
    switch (status) {
      case 'Open': return 'bg-green-100 text-green-800';
      case 'Draft': return 'bg-gray-100 text-gray-800';
      case 'Filled': return 'bg-blue-100 text-blue-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  }

  // Get priority styling
  function getPriorityClass(priority: string) {
    switch (priority) {
      case 'A': return 'bg-red-100 text-red-800';
      case 'B': return 'bg-orange-100 text-orange-800';
      case 'C': return 'bg-yellow-100 text-yellow-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  }
</script>

<svelte:head>
  <title>Preview Pattern - Demo</title>
</svelte:head>

<div class="space-y-6 relative">
  <!-- Demo Description -->
  <Card>
    <CardHeader>
      <CardTitle class="text-lg">Quick Preview Popover Pattern</CardTitle>
    </CardHeader>
    <CardContent>
      <p class="text-muted-foreground text-sm">
        Hover over job rows to see quick preview. This allows rapid scanning while keeping list context.
      </p>
    </CardContent>
  </Card>

  <!-- Jobs List with Hover Previews -->
  <Card>
    <CardHeader>
      <CardTitle>Jobs List</CardTitle>
    </CardHeader>
    <CardContent>
      <div class="overflow-x-auto">
        <table class="w-full border-collapse">
          <thead>
            <tr class="border-b">
              <th class="text-left p-3 font-medium text-muted-foreground">Job Title</th>
              <th class="text-left p-3 font-medium text-muted-foreground">Company</th>
              <th class="text-left p-3 font-medium text-muted-foreground">Location</th>
              <th class="text-left p-3 font-medium text-muted-foreground">Salary</th>
              <th class="text-left p-3 font-medium text-muted-foreground">Status</th>
              <th class="text-left p-3 font-medium text-muted-foreground">Priority</th>
              <th class="text-left p-3 font-medium text-muted-foreground">Posted</th>
            </tr>
          </thead>
          <tbody>
            {#each mockJobs as job}
              <tr
                class="border-b hover:bg-muted/50 cursor-pointer transition-colors"
                onmouseenter={(e) => showQuickPreview(job, e)}
                onmouseleave={hidePreview}
                onclick={() => viewFullDetails(job.id)}
              >
                <td class="p-3">
                  <div class="font-medium">{job.title}</div>
                </td>
                <td class="p-3 text-muted-foreground">{job.company}</td>
                <td class="p-3 text-muted-foreground">{job.location}</td>
                <td class="p-3 text-muted-foreground">{job.salary}</td>
                <td class="p-3">
                  <span class="px-2 py-1 text-xs font-medium rounded-full {getStatusClass(job.status)}">
                    {job.status}
                  </span>
                </td>
                <td class="p-3">
                  <span class="px-2 py-1 text-xs font-medium rounded-full {getPriorityClass(job.priority)}">
                    {job.priority}
                  </span>
                </td>
                <td class="p-3 text-muted-foreground text-sm">{job.posted}</td>
              </tr>
            {/each}
          </tbody>
        </table>
      </div>
    </CardContent>
  </Card>

  <!-- Pattern Notes -->
  <Card>
    <CardHeader>
      <CardTitle class="text-sm">Pattern Notes</CardTitle>
    </CardHeader>
    <CardContent class="text-xs space-y-2">
      <div><strong>Pros:</strong> Quick scanning, keeps list context, progressive disclosure, reduces clicks</div>
      <div><strong>Cons:</strong> Limited space, hover dependency, mobile challenges, can feel overwhelming</div>
      <div><strong>Best for:</strong> Quick comparisons, screening workflows, summary information</div>
    </CardContent>
  </Card>

  <!-- Custom Popover -->
  {#if hoveredJobId && selectedJob}
    <div 
      class="fixed z-50 w-80 bg-background border shadow-xl rounded-lg pointer-events-none"
      style="left: {popoverPosition.x}px; top: {popoverPosition.y}px; max-width: 320px;"
    >
      <div class="p-4 space-y-3">
        <!-- Header -->
        <div>
          <h3 class="font-semibold text-sm">{selectedJob.title}</h3>
          <p class="text-xs text-muted-foreground">{selectedJob.company}</p>
        </div>
        
        <!-- Quick Info -->
        <div class="grid grid-cols-2 gap-2 text-xs">
          <div>
            <span class="font-medium">Location:</span>
            <p class="text-muted-foreground">{selectedJob.location}</p>
          </div>
          <div>
            <span class="font-medium">Type:</span>
            <p class="text-muted-foreground">{selectedJob.type}</p>
          </div>
        </div>
        
        <!-- Description Preview -->
        <div>
          <span class="font-medium text-xs">Description:</span>
          <p class="text-xs text-muted-foreground mt-1 leading-relaxed">
            {selectedJob.description}
          </p>
        </div>
        
        <!-- Key Requirements -->
        <div>
          <span class="font-medium text-xs">Key Requirements:</span>
          <ul class="text-xs text-muted-foreground mt-1 space-y-0.5">
            {#each selectedJob.requirements.slice(0, 2) as req}
              <li class="flex items-start gap-1">
                <span class="text-green-600 text-xs">•</span>
                <span>{req}</span>
              </li>
            {/each}
            {#if selectedJob.requirements.length > 2}
              <li class="text-muted-foreground/70 italic">
                +{selectedJob.requirements.length - 2} more requirements...
              </li>
            {/if}
          </ul>
        </div>
        
        <!-- Note -->
        <div class="border-t pt-2">
          <p class="text-xs text-muted-foreground/70 italic">
            Click row for full details
          </p>
        </div>
      </div>
    </div>
  {/if}
</div>
