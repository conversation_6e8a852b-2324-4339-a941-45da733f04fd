# Cloudflare Workers Deployment Guide

## 🎯 Overview

Your SourceFlex application is now configured to deploy to **Cloudflare Workers** (not Pages) following Cloudflare's 2024 recommendation that Workers is the preferred platform for new projects.

## ✅ What's Been Configured

### 1. **SvelteKit Adapter**
- Using `@sveltejs/adapter-cloudflare` targeting Workers
- Configured for static assets + serverless functions
- TypeScript support with Cloudflare Workers types

### 2. **Build Configuration**
- `wrangler.toml` configured for Workers deployment
- Static assets served from `.svelte-kit/cloudflare`
- Worker script at `.svelte-kit/cloudflare/_worker.js`

### 3. **Package Scripts**
- `npm run build:cf` - Build for Cloudflare Workers
- `npm run preview:cf` - Local preview with Wrangler
- `npm run deploy:cf` - Deploy to production
- `npm run deploy:cf:preview` - Deploy to preview environment

### 4. **GitHub Actions**
- Automatic deployment on push to `main` branch
- Preview deployments for pull requests
- Located at `.github/workflows/deploy.yml`

## 🚀 Deployment Options

### Option 1: GitHub Actions (Recommended)

1. **Set up Cloudflare API credentials:**
   ```bash
   # Get your API token from Cloudflare Dashboard > My Profile > API Tokens
   # Create token with "Custom token" with these permissions:
   # - Account: Cloudflare Workers:Edit
   # - Zone: Zone:Read, Zone:Edit
   # - Account: Account:Read
   ```

2. **Add GitHub Secrets:**
   - Go to your GitHub repository settings
   - Navigate to Secrets and Variables > Actions
   - Add these secrets:
     - `CLOUDFLARE_API_TOKEN`: Your Cloudflare API token
     - `CLOUDFLARE_ACCOUNT_ID`: Your Cloudflare Account ID

3. **Push to main branch:**
   ```bash
   git add .
   git commit -m "Configure Cloudflare Workers deployment"
   git push origin main
   ```

### Option 2: Manual Deployment

1. **Authenticate with Cloudflare:**
   ```bash
   npx wrangler login
   ```

2. **Deploy manually:**
   ```bash
   npm run deploy:cf
   ```

## 🔧 Configuration Files

### `wrangler.toml`
```toml
name = "sourceflex"
main = ".svelte-kit/cloudflare/_worker.js"
compatibility_date = "2024-07-17"

[assets]
directory = ".svelte-kit/cloudflare"
binding = "ASSETS"

[vars]
NODE_ENV = "production"

compatibility_flags = ["nodejs_compat"]
```

### `svelte.config.js`
- Configured with Cloudflare adapter
- Platform proxy for local development
- Route configuration for prerendering

## 🌐 Automatic Updates

Once set up, your application will automatically:

1. **Deploy to production** when you push to `main` branch
2. **Create preview deployments** for pull requests
3. **Update your live site** within minutes of pushing changes

## 🔍 Monitoring & URLs

- **Production URL**: `https://sourceflex.<your-subdomain>.workers.dev`
- **Custom Domain**: Configure in Cloudflare Dashboard
- **Logs**: Available in Cloudflare Dashboard > Workers & Pages > sourceflex

## 🛠 Local Development

```bash
# Start development server
npm run dev

# Build for Cloudflare
npm run build:cf

# Preview with Wrangler (simulates Workers environment)
npm run preview:cf
```

## 📋 Next Steps

1. **Set up GitHub Secrets** (if using GitHub Actions)
2. **Push to main branch** to trigger first deployment
3. **Configure custom domain** (optional) in Cloudflare Dashboard
4. **Set up environment variables** for production in Cloudflare Dashboard

## 🔄 Migration from Vercel

Your application is now fully migrated from Vercel to Cloudflare Workers:
- ✅ Static assets served efficiently
- ✅ Serverless functions via Workers
- ✅ Automatic deployments configured
- ✅ Preview environments for PRs
- ✅ Future-proof platform (Cloudflare's focus)

## 🆘 Troubleshooting

### Build Issues
```bash
# Clear build cache
rm -rf .svelte-kit node_modules
npm install
npm run build:cf
```

### Authentication Issues
```bash
# Re-authenticate with Cloudflare
npx wrangler logout
npx wrangler login
```

### Deployment Issues
- Check GitHub Actions logs
- Verify API token permissions
- Ensure Account ID is correct

## 📚 Resources

- [Cloudflare Workers Documentation](https://developers.cloudflare.com/workers/)
- [SvelteKit Cloudflare Adapter](https://kit.svelte.dev/docs/adapter-cloudflare)
- [Wrangler CLI Documentation](https://developers.cloudflare.com/workers/wrangler/)
