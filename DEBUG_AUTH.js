// EMERGENCY DEBUG SCRIPT
// Paste this into browser console to debug the auth issue

console.log('=== SOURCEFLEX AUTH DEBUG SCRIPT ===');

// Check current nHost session
console.log('1. nHost session:', window.nhost?.auth?.getSession());

// Check localStorage
console.log('2. localStorage keys:', Object.keys(localStorage));
console.log('   localStorage contents:', {...localStorage});

// Check sessionStorage
console.log('3. sessionStorage keys:', Object.keys(sessionStorage));
console.log('   sessionStorage contents:', {...sessionStorage});

// Check cookies
console.log('4. Cookies:', document.cookie);

// Check IndexedDB databases
indexedDB.databases().then(databases => {
  console.log('5. IndexedDB databases:', databases);
});

// Check Svelte stores
import { get } from 'svelte/store';
import { session, isAuthenticated } from '$lib/stores/auth.js';

console.log('6. Svelte session store:', get(session));
console.log('7. isAuthenticated:', get(isAuthenticated));

// NUCLEAR RESET FUNCTION
window.nuclearReset = async () => {
  console.log('🚨 NUCLEAR RESET ACTIVATED 🚨');
  
  // Import auth actions
  const { authActions } = await import('$lib/stores/auth.js');
  
  // Call nuclear option
  await authActions.forceSessionReset();
};

console.log('=== DEBUG COMPLETE ===');
console.log('Run window.nuclearReset() to force complete reset');
